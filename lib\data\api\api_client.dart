import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/common/models/error_model.dart';
import 'package:six_cash/util/app_constants.dart';

class MultipartBody {
  String key;
  File? file;

  MultipartBody(this.key, this.file);
}

class ApiClient extends GetxService {
   String appBaseUrl = AppConstants.baseUrl ;
  final SharedPreferences sharedPreferences;
  final String noInternetMessage = 'Connection to API server failed due to internet connection';
  final int timeoutInSeconds = 30;
  BaseDeviceInfo deiceInfo;
  final String uniqueId;
  String? token;
  Map<String, String>? _mainHeaders;

  ApiClient({
    required this.appBaseUrl,
    required this.sharedPreferences,
    required this.deiceInfo,
    required this.uniqueId,

  })
  {

    _mainHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'Dart/2.17 (dart:io)',
      'Authorization': 'Bearer $token',
    };

    if(('${deiceInfo.data['isPhysicalDevice']}' == 'true') || AppConstants.demo) {
      _mainHeaders!.addAll({
        'device-id': uniqueId,
        'os': GetPlatform.isAndroid ? 'Android' : 'iOS',
        'device-model': '${deiceInfo.data['brand']} ${deiceInfo.data['model']}',
        'browser': 'Mobile App'
      });
    }
  }

   void updateHeader(String token) {
     _mainHeaders = {
       'Content-Type': 'application/json',
       'User-Agent': 'Dart/2.17 (dart:io)',
       'Authorization': 'Bearer $token',
     };

     if(('${deiceInfo.data['isPhysicalDevice']}' == 'true') || AppConstants.demo) {
       _mainHeaders!.addAll({
         'device-id': uniqueId,
         'os': GetPlatform.isAndroid ? 'Android' : 'iOS',
         'device-model': '${GetPlatform.isAndroid
             ? '${deiceInfo.data['brand']} ${deiceInfo.data['device-model']}'
             : ''} ${deiceInfo.data['model']}'.replaceAll(' null ', ' '),
         'browser': 'Mobile App'
       });
     }
   }



   Future<Response> getData(String uri, {Map<String, dynamic>? query, Map<String, String>? headers}) async {
    if(await ApiChecker.isVpnActive()) {
      return const Response(statusCode: -1, statusText: 'you are using vpn');
    }else{

      // Demo mode handling
      if (AppConstants.demo) {
        debugPrint('====> DEMO MODE: API Call: $uri');
        return _getDemoResponse(uri);
      }

      try {
        debugPrint('====> API Call: $uri\nHeader: $_mainHeaders');
        http.Response response = await http.get(
          Uri.parse(appBaseUrl+uri),
          headers: headers ?? _mainHeaders,
        ).timeout(Duration(seconds: timeoutInSeconds));
        return handleResponse(response, uri);
      } catch (e) {
        return Response(statusCode: 1, statusText: noInternetMessage);
      }

    }
   }

  Future<Response> postData(
      String uri, dynamic body, {Map<String, String>? headers}) async {
    if(await ApiChecker.isVpnActive()) {
      return const Response(statusCode: -1, statusText: 'you are using vpn');
    }{

      // Demo mode handling
      if (AppConstants.demo) {
        debugPrint('====> DEMO MODE: POST Call: $uri');
        debugPrint('====> DEMO MODE: POST Body: $body');
        return _getDemoResponse(uri);
      }

      try {
        debugPrint('====> GetX Base URL: $appBaseUrl');
        debugPrint('====> GetX Call: $uri');
        debugPrint('====> GetX Body: $body');

        http.Response response0 = await http.post(
          Uri.parse(appBaseUrl+uri),
          body: jsonEncode(body),
          headers: headers ?? _mainHeaders,
        ).timeout(Duration(seconds: timeoutInSeconds));
        Response response = handleResponse(response0, uri);
        return response;

      } catch (e) {
        return Response(statusCode: 1, statusText: noInternetMessage);
      }

    }
  }
   Future<Response> postMultipartData(String uri, Map<String, String> body, List<MultipartBody>? multipartBody, {Map<String, String>? headers}) async {

     if(await ApiChecker.isVpnActive()) {
       return const Response(statusCode: -1, statusText: 'you are using vpn');
     }{
       try {
         debugPrint('====> API Call: $uri\nToken: $token');
         debugPrint('====> API Body: $body with ${multipartBody!.length} image ');
         http.MultipartRequest request = http.MultipartRequest('POST', Uri.parse(appBaseUrl+uri));
         request.headers.addAll(headers ?? _mainHeaders!);
         for(MultipartBody multipart in multipartBody) {
           if(multipart.file != null) {
             if(kIsWeb) {
               Uint8List list = await multipart.file!.readAsBytes();
               http.MultipartFile part = http.MultipartFile(
                 multipart.key, multipart.file!.readAsBytes().asStream(), list.length,
                 filename: multipart.file!.path,
               );
               request.files.add(part);
             }else {
               File file = File(multipart.file!.path);
               request.files.add(http.MultipartFile(
                 multipart.key, file.readAsBytes().asStream(), file.lengthSync(), filename: file.path.split('/').last,
               ));
             }
           }
         }
         request.fields.addAll(body);
         http.Response response0 = await http.Response.fromStream(await request.send());
         Response response = handleResponse(response0, uri);
         
         debugPrint('====> API Response: [${response.statusCode}] $uri\n${response.body}');
         
         return response;
       } catch (e) {
         return Response(statusCode: 1, statusText: noInternetMessage);
       }
     }

   }


   Future<Response> putData(
    String uri,
    dynamic body, {
    Map<String, dynamic>? query,
    String? contentType,
    Map<String, String>? headers,
    Function(dynamic)? decoder,
    Function(double)? uploadProgress
  }) async {
     if(await ApiChecker.isVpnActive()) {
       return const Response(statusCode: -1, statusText: 'you are using vpn');
     } {
       try {
         debugPrint('====> GetX Call: $uri');
         debugPrint('====> GetX Body: $body');
         
         http.Response response0 = await http.put(
           Uri.parse(appBaseUrl+uri),
           body: jsonEncode(body),
           headers: headers ?? _mainHeaders,
         ).timeout(Duration(seconds: timeoutInSeconds));
         Response response = handleResponse(response0, uri);
         
         debugPrint('====> API Response: [${response.statusCode}] $uri\n${response.body}');
         
         return response;

       } catch (e) {
         return Response(statusCode: 1, statusText: noInternetMessage);
       }

     }

   }
   Future<Response> putMultipartData(String uri, Map<String, String> body, List<MultipartBody> multipartBody, {Map<String, String>? headers}) async {

     if(await ApiChecker.isVpnActive()) {
       return const Response(statusCode: -1, statusText: 'you are using vpn');
     } {
       try {
         debugPrint('====> API Call: $uri\nToken: $token');
         debugPrint('====> API Body: $body');
         
         http.MultipartRequest request = http.MultipartRequest('PUT', Uri.parse(appBaseUrl+uri));
         request.headers.addAll(headers ?? _mainHeaders!);
         for(MultipartBody multipart in multipartBody) {
           if(multipart.file != null) {
             if(kIsWeb) {
               Uint8List list = await multipart.file!.readAsBytes();
               http.MultipartFile part = http.MultipartFile(
                 multipart.key, multipart.file!.readAsBytes().asStream(), list.length,
                 filename: multipart.file!.path,
               );
               request.files.add(part);
             }else {
               File file = File(multipart.file!.path);
               request.files.add(http.MultipartFile(
                 multipart.key, file.readAsBytes().asStream(), file.lengthSync(), filename: file.path.split('/').last,
               ));
             }
           }
         }
         request.fields.addAll(body);
         http.Response response0 = await http.Response.fromStream(await request.send());
         Response response = handleResponse(response0, uri);
         
         debugPrint('====> API Response: [${response.statusCode}] $uri\n${response.body}');

         return response;
       } catch (e) {
         return Response(statusCode: 1, statusText: noInternetMessage);
       }
     }

   }

   Future<Response> deleteData(String uri, {Map<String, String>? headers}) async {
     if(await ApiChecker.isVpnActive()) {
       return const Response(statusCode: -1, statusText: 'you are using vpn');
     } {
       try {
         debugPrint('====> API Call: $uri\nHeader: $_mainHeaders');
         http.Response response = await http.delete(
           Uri.parse(appBaseUrl+uri),
           headers: headers ?? _mainHeaders,
         ).timeout(Duration(seconds: timeoutInSeconds));
         return handleResponse(response, uri);
       } catch (e) {
         return Response(statusCode: 1, statusText: noInternetMessage);
       }
     }

   }

   Response handleResponse(http.Response response, String uri) {
     dynamic body;
     try {
       body = jsonDecode(response.body);
     }catch(e) {
       debugPrint('error ---> $e');
     }
     Response response0 = Response(
       body: body ?? response.body, bodyString: response.body.toString(),
       request: Request(headers: response.request!.headers, method: response.request!.method, url: response.request!.url),
       headers: response.headers, statusCode: response.statusCode, statusText: response.reasonPhrase,
     );
     if(response0.statusCode != 200 && response0.body != null && response0.body is !String) {
       if(response0.body.toString().startsWith('{errors: [{code:')) {
         ErrorResponseModel errorResponse = ErrorResponseModel.fromJson(response0.body);
         response0 = Response(statusCode: response0.statusCode, body: response0.body, statusText: errorResponse.errors![0].message);
       }else if(response0.body.toString().startsWith('{message')) {
         response0 = Response(statusCode: response0.statusCode, body: response0.body, statusText: response0.body['message']);
       }
     }else if(response0.statusCode != 200 && response0.body == null) {
       response0 = Response(statusCode: 0, statusText: noInternetMessage);
     }
     debugPrint('====> API Response: [${response0.statusCode}] $uri\n${response0.body}');
     return response0;
   }


  // Demo mode response handler
  Response _getDemoResponse(String uri) {
    Map<String, dynamic> demoData = {};

    // Transaction History
    if (uri.contains('transaction-history')) {
      demoData = {
        'total_size': 5,
        'limit': 1000,
        'offset': 1,
        'transactions': [
          {
            'id': 1,
            'transaction_id': 'TXN001',
            'amount': 2500.0,
            'charge': 25.0,
            'transaction_type': 'Send Money',
            'debit_credit': 'debit',
            'balance': 7500.0,
            'from_user': {
              'name': 'Demo User',
              'phone': '+*************'
            },
            'to_user': {
              'name': 'John Doe',
              'phone': '+2348012345678'
            },
            'created_at': '2024-01-15T14:30:00Z',
            'updated_at': '2024-01-15T14:30:00Z'
          },
          {
            'id': 2,
            'transaction_id': 'TXN002',
            'amount': 5000.0,
            'charge': 0.0,
            'transaction_type': 'Add Money',
            'debit_credit': 'credit',
            'balance': 10000.0,
            'from_user': null,
            'to_user': {
              'name': 'Demo User',
              'phone': '+*************'
            },
            'created_at': '2024-01-15T10:00:00Z',
            'updated_at': '2024-01-15T10:00:00Z'
          },
          {
            'id': 3,
            'transaction_id': 'TXN003',
            'amount': 1500.0,
            'charge': 15.0,
            'transaction_type': 'Cash Out',
            'debit_credit': 'debit',
            'balance': 8485.0,
            'from_user': {
              'name': 'Demo User',
              'phone': '+*************'
            },
            'to_user': {
              'name': 'Agent Smith',
              'phone': '+*************'
            },
            'created_at': '2024-01-14T16:45:00Z',
            'updated_at': '2024-01-14T16:45:00Z'
          }
        ]
      };
    }
    // Withdrawal Methods
    else if (uri.contains('withdrawal-methods')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Demo withdrawal methods',
        'content': [
          {
            'id': 1,
            'method_name': 'Bank Transfer',
            'method_fields': [
              {
                'input_name': 'account_number',
                'input_type': 'text',
                'placeholder': 'Account Number',
                'is_required': 1
              },
              {
                'input_name': 'bank_name',
                'input_type': 'text',
                'placeholder': 'Bank Name',
                'is_required': 1
              }
            ]
          },
          {
            'id': 2,
            'method_name': 'Mobile Money',
            'method_fields': [
              {
                'input_name': 'phone_number',
                'input_type': 'text',
                'placeholder': 'Phone Number',
                'is_required': 1
              }
            ]
          }
        ]
      };
    }
    // Customer Profile
    else if (uri.contains('get-customer')) {
      demoData = {
        'id': 1,
        'f_name': 'Demo',
        'l_name': 'User',
        'phone': '+*************',
        'email': '<EMAIL>',
        'image': 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=DU',
        'type': 'customer',
        'gender': 'male',
        'occupation': 'Software Developer',
        'balance': 7500.0,
        'two_factor': false,
        'kyc_status': 'approved',
        'qr_code': 'demo_qr_code_123',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-15T14:30:00Z'
      };
    }
    // Purpose List
    else if (uri.contains('get-purpose')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Demo purposes',
        'content': [
          {
            'id': 1,
            'title': 'Food & Groceries',
            'logo': 'https://via.placeholder.com/50/FF9800/FFFFFF?text=F',
            'color': '#FF9800'
          },
          {
            'id': 2,
            'title': 'Transportation',
            'logo': 'https://via.placeholder.com/50/2196F3/FFFFFF?text=T',
            'color': '#2196F3'
          },
          {
            'id': 3,
            'title': 'Bills & Utilities',
            'logo': 'https://via.placeholder.com/50/9C27B0/FFFFFF?text=B',
            'color': '#9C27B0'
          },
          {
            'id': 4,
            'title': 'Education',
            'logo': 'https://via.placeholder.com/50/4CAF50/FFFFFF?text=E',
            'color': '#4CAF50'
          }
        ]
      };
    }
    // Banners
    else if (uri.contains('get-banner')) {
      return Response(statusCode: 200, body: [
        {
          'id': 1,
          'title': 'Welcome to Demo Wallet',
          'image': 'https://via.placeholder.com/400x200/4CAF50/FFFFFF?text=Welcome+to+Demo+Wallet',
          'url': null,
          'status': 1
        },
        {
          'id': 2,
          'title': 'Send Money Instantly',
          'image': 'https://via.placeholder.com/400x200/2196F3/FFFFFF?text=Send+Money+Fast',
          'url': null,
          'status': 1
        }
      ]);
    }
    // Linked Websites
    else if (uri.contains('linked-website')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Demo linked websites',
        'content': []
      };
    }
    // Notifications
    else if (uri.contains('get-notification')) {
      demoData = {
        'total_size': 3,
        'limit': 10,
        'offset': 1,
        'notifications': [
          {
            'id': 1,
            'title': 'Welcome to Demo Wallet!',
            'description': 'Your account has been successfully created. Start sending and receiving money instantly.',
            'image': null,
            'status': 1,
            'created_at': '2024-01-15T10:00:00Z'
          },
          {
            'id': 2,
            'title': 'Transaction Successful',
            'description': 'You have successfully sent ₦2,500 to John Doe.',
            'image': null,
            'status': 1,
            'created_at': '2024-01-15T14:30:00Z'
          },
          {
            'id': 3,
            'title': 'Money Added',
            'description': 'Your wallet has been credited with ₦5,000.',
            'image': null,
            'status': 1,
            'created_at': '2024-01-15T10:00:00Z'
          }
        ]
      };
    }
    // Requested Money List
    else if (uri.contains('get-requested-money')) {
      demoData = {
        'total_size': 2,
        'limit': 10,
        'offset': 1,
        'requested_money': [
          {
            'id': 1,
            'amount': 1000.0,
            'note': 'For lunch',
            'status': 'pending',
            'sender': {
              'name': 'Alice Johnson',
              'phone': '+2348123456789'
            },
            'receiver': {
              'name': 'Demo User',
              'phone': '+*************'
            },
            'created_at': '2024-01-15T12:00:00Z'
          }
        ]
      };
    }
    // Own Requested Money List
    else if (uri.contains('get-own-requested-money')) {
      demoData = {
        'total_size': 1,
        'limit': 10,
        'offset': 1,
        'requested_money': [
          {
            'id': 2,
            'amount': 2000.0,
            'note': 'For transportation',
            'status': 'approved',
            'sender': {
              'name': 'Demo User',
              'phone': '+*************'
            },
            'receiver': {
              'name': 'Bob Smith',
              'phone': '+*************'
            },
            'created_at': '2024-01-14T15:30:00Z'
          }
        ]
      };
    }
    // Withdrawal Requests
    else if (uri.contains('withdrawal-requests')) {
      demoData = {
        'total_size': 1,
        'limit': 10,
        'offset': 1,
        'withdrawal_requests': [
          {
            'id': 1,
            'amount': 5000.0,
            'charge': 50.0,
            'method': 'Bank Transfer',
            'status': 'pending',
            'account_number': '**********',
            'bank_name': 'First Bank',
            'created_at': '2024-01-15T16:00:00Z'
          }
        ]
      };
    }
    // FAQ
    else if (uri.contains('faq')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Demo FAQ',
        'content': [
          {
            'id': 1,
            'question': 'How do I send money?',
            'answer': 'To send money, go to the Send Money section, enter the recipient\'s phone number and amount, then confirm the transaction.',
            'status': 1
          },
          {
            'id': 2,
            'question': 'What are the transaction limits?',
            'answer': 'You can send up to ₦50,000 per transaction and ₦100,000 per day.',
            'status': 1
          }
        ]
      };
    }
    // POST requests - Send Money, Request Money, Cash Out, etc.
    else if (uri.contains('send-money')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Money sent successfully',
        'content': {
          'transaction_id': 'TXN' + DateTime.now().millisecondsSinceEpoch.toString(),
          'amount': 1000.0,
          'charge': 10.0,
          'balance': 6490.0
        }
      };
    }
    else if (uri.contains('request-money')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Money request sent successfully',
        'content': {
          'request_id': 'REQ' + DateTime.now().millisecondsSinceEpoch.toString()
        }
      };
    }
    else if (uri.contains('cash-out')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Cash out successful',
        'content': {
          'transaction_id': 'TXN' + DateTime.now().millisecondsSinceEpoch.toString(),
          'amount': 1000.0,
          'charge': 15.0,
          'balance': 6475.0
        }
      };
    }
    else if (uri.contains('withdraw')) {
      demoData = {
        'response_code': 'default_store_200',
        'message': 'Withdrawal request submitted successfully',
        'content': {
          'request_id': 'WDR' + DateTime.now().millisecondsSinceEpoch.toString()
        }
      };
    }
    else if (uri.contains('add-money')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Add money request processed',
        'content': {
          'payment_url': 'https://demo-payment-gateway.com/pay?ref=' + DateTime.now().millisecondsSinceEpoch.toString()
        }
      };
    }
    else if (uri.contains('update-fcm-token')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'FCM token updated successfully'
      };
    }
    else if (uri.contains('verify-pin')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'PIN verified successfully'
      };
    }
    else if (uri.contains('request-money/approve') || uri.contains('request-money/deny')) {
      demoData = {
        'response_code': 'default_200',
        'message': 'Request processed successfully'
      };
    }
    // Default demo response for any other API call
    else {
      demoData = {
        'response_code': 'default_200',
        'message': 'Demo mode - API call successful',
        'content': {}
      };
    }

    return Response(statusCode: 200, body: demoData);
  }
}
