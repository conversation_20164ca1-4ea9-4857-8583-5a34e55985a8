import 'package:six_cash/data/api/api_checker.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:six_cash/features/setting/domain/models/requested_money_model.dart';
import 'package:six_cash/features/requested_money/domain/models/withdraw_histroy_model.dart';
import 'package:six_cash/features/requested_money/domain/reposotories/requested_money_repo.dart';
import 'package:six_cash/util/app_constants.dart';
import 'package:six_cash/helper/custom_snackbar_helper.dart';


class RequestedMoneyController extends GetxController implements GetxService {
  final RequestedMoneyRepo requestedMoneyRepo;
  RequestedMoneyController({required this.requestedMoneyRepo});



  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<RequestedMoney> _requestedMoneyList = [];
  List<RequestedMoney> _ownRequestList = [];

  List<RequestedMoney> get requestedMoneyList => _requestedMoneyList;
  List<RequestedMoney> get ownRequestList => _ownRequestList;

  List<RequestedMoney> _pendingRequestedMoneyList =[];
  List<RequestedMoney> _ownPendingRequestedMoneyList =[];

  List<RequestedMoney> get pendingRequestedMoneyList => _pendingRequestedMoneyList;
  List<RequestedMoney> get ownPendingRequestedMoneyList => _ownPendingRequestedMoneyList;

  List<RequestedMoney> _acceptedRequestedMoneyList =[];
  List<RequestedMoney> _ownAcceptedRequestedMoneyList =[];

  List<RequestedMoney> get acceptedRequestedMoneyList =>_acceptedRequestedMoneyList;
  List<RequestedMoney> get ownAcceptedRequestedMoneyList =>_ownAcceptedRequestedMoneyList;

  List<RequestedMoney> _deniedRequestedMoneyList =[];
  List<RequestedMoney> _ownDeniedRequestedMoneyList =[];

  List<RequestedMoney> get deniedRequestedMoneyList => _deniedRequestedMoneyList;
  List<RequestedMoney> get ownDeniedRequestedMoneyList => _ownDeniedRequestedMoneyList;

  WithdrawHistoryModel? _withdrawHistoryModel;
  WithdrawHistoryModel? get withdrawHistoryModel => _withdrawHistoryModel;

  List<WithdrawHistory> pendingWithdraw = [];
  List<WithdrawHistory> acceptedWithdraw = [];
  List<WithdrawHistory> deniedWithdraw = [];
  List<WithdrawHistory> allWithdraw = [];

  int? _pageSize;
  int? get pageSize => _pageSize;

  RequestedMoneyModel? _requestedMoneyModel;
  RequestedMoneyModel? _ownRequestMoneyModel;
  RequestedMoneyModel? get  ownRequestMoneyModel => _ownRequestMoneyModel;


  Future getRequestedMoneyList(bool reload, {bool isUpdate = true}) async{

    if(reload || _requestedMoneyModel == null) {
      _requestedMoneyModel = null;
      _isLoading = true;

      if(isUpdate){
        update();
      }
    }

    if(_requestedMoneyModel == null){
      // Demo mode handling
      if (AppConstants.demo) {
        List<Map<String, dynamic>> demoRequestedMoney = [
          {
            'id': 1,
            'amount': 2500.0,
            'note': 'Payment for lunch',
            'type': AppConstants.pending,
            'created_at': '2024-01-15T14:30:00Z',
            'updated_at': '2024-01-15T14:30:00Z',
            'sender': {
              'id': 2,
              'f_name': 'Alice',
              'l_name': 'Johnson',
              'phone': '+2349087654321',
              'image': 'alice.jpg'
            },
            'receiver': {
              'id': 1,
              'f_name': 'Demo',
              'l_name': 'User',
              'phone': '+2347026591356',
              'image': 'demo_user.jpg'
            }
          },
          {
            'id': 2,
            'amount': 1000.0,
            'note': 'Transport fare',
            'type': AppConstants.approved,
            'created_at': '2024-01-14T10:15:00Z',
            'updated_at': '2024-01-14T16:45:00Z',
            'sender': {
              'id': 3,
              'f_name': 'John',
              'l_name': 'Doe',
              'phone': '+2348123456789',
              'image': 'john.jpg'
            },
            'receiver': {
              'id': 1,
              'f_name': 'Demo',
              'l_name': 'User',
              'phone': '+2347026591356',
              'image': 'demo_user.jpg'
            }
          },
          {
            'id': 3,
            'amount': 500.0,
            'note': 'Coffee money',
            'type': AppConstants.denied,
            'created_at': '2024-01-13T09:20:00Z',
            'updated_at': '2024-01-13T09:25:00Z',
            'sender': {
              'id': 4,
              'f_name': 'Sarah',
              'l_name': 'Wilson',
              'phone': '+2348765432109',
              'image': 'sarah.jpg'
            },
            'receiver': {
              'id': 1,
              'f_name': 'Demo',
              'l_name': 'User',
              'phone': '+2347026591356',
              'image': 'demo_user.jpg'
            }
          }
        ];

        _requestedMoneyModel = RequestedMoneyModel.fromJson({
          'total_size': demoRequestedMoney.length,
          'limit': 20,
          'offset': 1,
          'requested_money': demoRequestedMoney
        });

        _requestedMoneyList = [];
        _pendingRequestedMoneyList =[];
        _acceptedRequestedMoneyList =[];
        _deniedRequestedMoneyList =[];

        _requestedMoneyModel?.requestedMoney?.forEach((req) {
          if(req.sender != null) {
            if(req.type == AppConstants.approved){
              _acceptedRequestedMoneyList.add(req);
            }else if(req.type == AppConstants.pending){
              _pendingRequestedMoneyList.add(req);
            }else if(req.type == AppConstants.denied){
              _deniedRequestedMoneyList.add(req);
            }
            _requestedMoneyList.add(req);
          }
        });

        _isLoading = false;
        update();
        return;
      }

      Response response = await requestedMoneyRepo.getRequestedMoneyList();
      if(response.statusCode == 200){
        _requestedMoneyModel = RequestedMoneyModel.fromJson(response.body);

        _requestedMoneyList = [];
        _pendingRequestedMoneyList =[];
        _acceptedRequestedMoneyList =[];
        _deniedRequestedMoneyList =[];

        _requestedMoneyModel?.requestedMoney?.forEach((req) {
          if(req.sender != null) {
            if(req.type == AppConstants.approved){
              _acceptedRequestedMoneyList.add(req);
            }else if(req.type == AppConstants.pending){
              _pendingRequestedMoneyList.add(req);
            }else if(req.type == AppConstants.denied){
              _deniedRequestedMoneyList.add(req);
            }
            _requestedMoneyList.add(req);
          }
        });

      }else{
        ApiChecker.checkApi(response);
      }
      _isLoading = false;
      update();

    }

  }

  Future getOwnRequestedMoneyList(bool reload, {bool isUpdate = true}) async{

    if(reload || _ownRequestMoneyModel == null) {
      _ownRequestMoneyModel = null;
      _isLoading = true;
      if(isUpdate){
        update();
      }
    }
    if(_ownRequestMoneyModel == null){
      // Demo mode handling
      if (AppConstants.demo) {
        List<Map<String, dynamic>> demoOwnRequests = [
          {
            'id': 4,
            'amount': 3000.0,
            'note': 'Grocery shopping',
            'type': AppConstants.pending,
            'created_at': '2024-01-15T12:00:00Z',
            'updated_at': '2024-01-15T12:00:00Z',
            'sender': {
              'id': 1,
              'f_name': 'Demo',
              'l_name': 'User',
              'phone': '+2347026591356',
              'image': 'demo_user.jpg'
            },
            'receiver': {
              'id': 5,
              'f_name': 'Michael',
              'l_name': 'Brown',
              'phone': '+2348901234567',
              'image': 'michael.jpg'
            }
          },
          {
            'id': 5,
            'amount': 1500.0,
            'note': 'Book purchase',
            'type': AppConstants.approved,
            'created_at': '2024-01-14T08:30:00Z',
            'updated_at': '2024-01-14T15:20:00Z',
            'sender': {
              'id': 1,
              'f_name': 'Demo',
              'l_name': 'User',
              'phone': '+2347026591356',
              'image': 'demo_user.jpg'
            },
            'receiver': {
              'id': 6,
              'f_name': 'Emma',
              'l_name': 'Davis',
              'phone': '+2347890123456',
              'image': 'emma.jpg'
            }
          }
        ];

        _ownRequestMoneyModel = RequestedMoneyModel.fromJson({
          'total_size': demoOwnRequests.length,
          'limit': 20,
          'offset': 1,
          'requested_money': demoOwnRequests
        });

        _ownRequestList =[];
        _ownPendingRequestedMoneyList = [];
        _ownAcceptedRequestedMoneyList = [];
        _ownDeniedRequestedMoneyList = [];

        _ownRequestMoneyModel?.requestedMoney?.forEach((requested) {

          if(requested.receiver != null){
            _ownRequestList.add(requested);
            if(requested.type == AppConstants.approved){
              _ownAcceptedRequestedMoneyList.add(requested);
            }else if(requested.type == AppConstants.pending){
              _ownPendingRequestedMoneyList.add(requested);
            }else if(requested.type == AppConstants.denied){
              _ownDeniedRequestedMoneyList.add(requested);
            }
          }

        });

        _isLoading = false;
        update();
        return;
      }

      Response response = await requestedMoneyRepo.getOwnRequestedMoneyList();

      if(response.statusCode == 200 && response.body['requested_money'] != null){

        _ownRequestMoneyModel = RequestedMoneyModel.fromJson(response.body);

        _ownRequestList =[];
        _ownPendingRequestedMoneyList = [];
        _ownAcceptedRequestedMoneyList = [];
        _ownDeniedRequestedMoneyList = [];

        _ownRequestMoneyModel?.requestedMoney?.forEach((requested) {

          if(requested.receiver != null){
            _ownRequestList.add(requested);
            if(requested.type == AppConstants.approved){
              _ownAcceptedRequestedMoneyList.add(requested);
            }else if(requested.type == AppConstants.pending){
              _ownPendingRequestedMoneyList.add(requested);
            }else if(requested.type == AppConstants.denied){
              _ownDeniedRequestedMoneyList.add(requested);
            }
          }


        });
      }else {
        ApiChecker.checkApi(response);
      }
      _isLoading = false;
      update();
    }

  }

  Future<void> acceptRequest(BuildContext context, int? requestId, String pin) async {
    _isLoading = true;
    update();
    Response response = await requestedMoneyRepo.approveRequestedMoney(requestId, pin);

    if(response.statusCode == 200) {
      await getRequestedMoneyList(true);
      Get.back();
      Get.back();
      _isLoading = false;
    }else {
      _isLoading = false;
      ApiChecker.checkApi(response);
    }
   update();
  }
  Future<void> denyRequest(BuildContext context, int? requestId, String pin ) async {
    _isLoading = true;
    update();
    Response response = await requestedMoneyRepo.denyRequestedMoney(requestId, pin);
    if(response.statusCode == 200) {
      await getRequestedMoneyList(true);
      Get.back();
      showCustomSnackBarHelper('request_denied_successfully'.tr, isError: false);
      _isLoading = false;
    }else {
      _isLoading = false;
      ApiChecker.checkApi(response);
    }
    update();
  }


  int _requestTypeIndex = 0;
  int get requestTypeIndex => _requestTypeIndex;

  void setIndex(int index, {bool isUpdate = true}) {
    _requestTypeIndex = index;
    if(isUpdate){
      update();
    }
  }

  void showBottomLoader() {
    _isLoading = true;
    update();
  }

  Future getWithdrawHistoryList({bool reload = false,  bool isUpdate = true}) async{

    if(reload || _withdrawHistoryModel == null) {
      _withdrawHistoryModel = null;
      _isLoading = true;
      if(isUpdate){
        update();
      }
    }

    if(_withdrawHistoryModel == null) {
      Response response = await requestedMoneyRepo.getWithdrawRequest();
      if(response.body['response_code'] == 'default_200' && response.body['content'] != null){

        pendingWithdraw = [];
        acceptedWithdraw = [];
        deniedWithdraw = [];
        allWithdraw = [];

        _withdrawHistoryModel = WithdrawHistoryModel.fromJson(response.body);

        for (var withdrawHistory in _withdrawHistoryModel!.withdrawHistoryList) {
          pendingWithdraw.addIf(withdrawHistory.requestStatus == AppConstants.pending, withdrawHistory);
          acceptedWithdraw.addIf(withdrawHistory.requestStatus == AppConstants.approved, withdrawHistory);
          deniedWithdraw.addIf(withdrawHistory.requestStatus == AppConstants.denied, withdrawHistory);
          allWithdraw.add(withdrawHistory);
        }

      }
      else{
        ApiChecker.checkApi(response);
      }

    }
    _isLoading = false;
    update();

  }

}