import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/features/home/<USER>/models/banner_model.dart';
import 'package:six_cash/features/home/<USER>/reposotories/banner_repo.dart';
import 'package:six_cash/util/app_constants.dart';
import 'package:get/get.dart';

class BannerController extends GetxController implements GetxService {
  final BannerRepo bannerRepo;
  BannerController({required this.bannerRepo});


  List<BannerModel>? _bannerList;
  int _bannerActiveIndex = 0;

  List<BannerModel>? get bannerList => _bannerList;
  int get bannerActiveIndex => _bannerActiveIndex;


  Future getBannerList(bool reload, {bool isUpdate = true})async{
    if(_bannerList == null || reload) {
      _bannerList = null;
      if (isUpdate) {
        update();
      }
    }
    if (_bannerList == null) {
      // Demo mode handling
      if (AppConstants.demo) {
        _bannerList = [
          BannerModel.fromJson({
            'id': 1,
            'title': 'Welcome to 6Cash',
            'image': 'https://via.placeholder.com/400x200/4CAF50/FFFFFF?text=Welcome+to+6Cash',
            'url': '',
            'status': 1
          }),
          BannerModel.fromJson({
            'id': 2,
            'title': 'Send Money Instantly',
            'image': 'https://via.placeholder.com/400x200/2196F3/FFFFFF?text=Send+Money+Fast',
            'url': '',
            'status': 1
          }),
          BannerModel.fromJson({
            'id': 3,
            'title': 'Secure Transactions',
            'image': 'https://via.placeholder.com/400x200/FF9800/FFFFFF?text=Secure+%26+Safe',
            'url': '',
            'status': 1
          })
        ];
        update();
        return;
      }

      Response response = await bannerRepo.getBannerList();
      if (response.statusCode == 200) {
        _bannerList = [];
        response.body.forEach((banner) {
          _bannerList!.add(BannerModel.fromJson(banner));
        });
      } else {
        _bannerList = [];
        ApiChecker.checkApi(response);
      }
      update();
    }
  }


  void updateBannerActiveIndex(int index) {
    _bannerActiveIndex = index;
    update();
  }
}