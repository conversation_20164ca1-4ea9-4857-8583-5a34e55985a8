import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/common/models/config_model.dart';
import 'package:six_cash/features/splash/domain/reposotories/splash_repo.dart';
import 'package:six_cash/util/app_constants.dart';

class SplashController extends GetxController implements GetxService{
   final SplashRepo splashRepo;
  SplashController({required this.splashRepo});

  ConfigModel? _configModel;

  final DateTime _currentTime = DateTime.now();

  DateTime get currentTime => _currentTime;
  bool _firstTimeConnectionCheck = true;
  bool get firstTimeConnectionCheck => _firstTimeConnectionCheck;

  ConfigModel? get configModel => _configModel;

  Future<Response> getConfigData() async {
    if (AppConstants.demo) {
      // Create a comprehensive mock config for demo mode
      _configModel = ConfigModel(
        companyName: 'Demo Wallet',
        country: 'NG',
        currencySymbol: '₦',
        currencyPosition: 'left',
        cashOutChargePercent: 2.0,
        sendMoneyChargeFlat: 10.0,
        withdrawChargePercent: 1.5,
        twoFactor: false,
        selfDelete: false,
        phoneVerification: true,
        languageCode: 'en',
        themeIndex: 1,
        otpResendTime: 60,
        systemFeature: SystemFeature(
          addMoneyStatus: true,
          sendMoneyStatus: true,
          cashOutStatus: true,
          sendMoneyRequestStatus: true,
          withdrawRequestStatus: true,
          linkedWebSiteStatus: false,
          bannerStatus: true,
        ),
        customerSendMoneyLimit: CustomerLimit(
          status: true,
          transactionLimitPerDay: 10,
          maxAmountPerTransaction: 50000.0,
          totalTransactionAmountPerDay: 100000.0,
          transactionLimitPerMonth: 100,
          totalTransactionAmountPerMonth: 1000000.0,
        ),
        customerAddMoneyLimit: CustomerLimit(
          status: true,
          transactionLimitPerDay: 10,
          maxAmountPerTransaction: 100000.0,
          totalTransactionAmountPerDay: 200000.0,
          transactionLimitPerMonth: 100,
          totalTransactionAmountPerMonth: 2000000.0,
        ),
        customerRequestMoneyLimit: CustomerLimit(
          status: true,
          transactionLimitPerDay: 5,
          maxAmountPerTransaction: 25000.0,
          totalTransactionAmountPerDay: 50000.0,
          transactionLimitPerMonth: 50,
          totalTransactionAmountPerMonth: 500000.0,
        ),
        customerWithdrawLimit: CustomerLimit(
          status: true,
          transactionLimitPerDay: 5,
          maxAmountPerTransaction: 25000.0,
          totalTransactionAmountPerDay: 50000.0,
          transactionLimitPerMonth: 50,
          totalTransactionAmountPerMonth: 500000.0,
        ),
        customerCashOutLimit: CustomerLimit(
          status: true,
          transactionLimitPerDay: 5,
          maxAmountPerTransaction: 30000.0,
          totalTransactionAmountPerDay: 60000.0,
          transactionLimitPerMonth: 50,
          totalTransactionAmountPerMonth: 600000.0,
        ),
      );
      update();
      return const Response(statusCode: 200, body: {});
    }

    Response response = await splashRepo.getConfigData();
    if(response.statusCode == 200){
      _configModel =  ConfigModel.fromJson(response.body);
    }
   else {
     ApiChecker.checkApi(response);
   }
    update();
    return response;

  }

  Future<bool> initSharedData() {
    return splashRepo.initSharedData();
  }

   void removeSharedData() {
    return splashRepo.removeSharedData();
  }

  bool isRestaurantClosed() {
    DateTime open = DateFormat('hh:mm').parse('');
    DateTime close = DateFormat('hh:mm').parse('');
    DateTime openTime = DateTime(_currentTime.year, _currentTime.month, _currentTime.day, open.hour, open.minute);
    DateTime closeTime = DateTime(_currentTime.year, _currentTime.month, _currentTime.day, close.hour, close.minute);
    if(closeTime.isBefore(openTime)) {
      closeTime = closeTime.add(const Duration(days: 1));
    }
    if(_currentTime.isAfter(openTime) && _currentTime.isBefore(closeTime)) {
      return false;
    }else {
      return true;
    }
  }


  void setFirstTimeConnectionCheck(bool isChecked) {
    _firstTimeConnectionCheck = isChecked;
  }

}
