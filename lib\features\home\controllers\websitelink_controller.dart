
import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/features/home/<USER>/models/websitelink_model.dart';
import 'package:six_cash/features/home/<USER>/reposotories/websitelink_repo.dart';
import 'package:get/get.dart';
import 'package:six_cash/util/app_constants.dart';

class WebsiteLinkController extends GetxController implements GetxService{
  final WebsiteLinkRepo websiteLinkRepo;
  WebsiteLinkController({required this.websiteLinkRepo});
  bool _isLoading = false;
  List<WebsiteLinkModel>? _websiteList;

  bool get isLoading => _isLoading;
  List<WebsiteLinkModel>? get websiteList => _websiteList;

  Future getWebsiteList(bool reload , {bool isUpdate = true}) async {
    if(_websiteList == null || reload) {
      _websiteList = null;
      _isLoading = true;

      if(isUpdate){
        update();
      }
    }
    if(_websiteList == null ) {
      _websiteList = [];

      // Demo mode handling
      if (AppConstants.demo) {
        List<Map<String, dynamic>> demoWebsites = [
          {
            'id': 1,
            'name': 'Official Website',
            'url': 'https://6cash.com',
            'image': 'website_official.png',
            'status': 1,
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z'
          },
          {
            'id': 2,
            'name': 'Support Center',
            'url': 'https://support.6cash.com',
            'image': 'website_support.png',
            'status': 1,
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z'
          },
          {
            'id': 3,
            'name': 'Blog',
            'url': 'https://blog.6cash.com',
            'image': 'website_blog.png',
            'status': 1,
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z'
          }
        ];

        _websiteList = [];
        for (var website in demoWebsites) {
          _websiteList!.add(WebsiteLinkModel.fromJson(website));
        }

        _isLoading = false;
        update();
        return;
      }

      Response response = await websiteLinkRepo.getWebsiteListApi();
      if(response.body != null && response.body != {} && response.statusCode == 200){
        _websiteList = [];
        response.body.forEach((website) {_websiteList!.add(WebsiteLinkModel.fromJson(website));});
      }else{
        _websiteList = [];
        ApiChecker.checkApi(response);

      }

      _isLoading = false;
      update();

    }
  }


}