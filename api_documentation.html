<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arefan Wallet - Customer API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #334155;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar {
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            height: calc(100vh - 76px);
            overflow-y: auto;
            position: sticky;
            top: 76px;
        }
        
        .sidebar .nav-link {
            color: var(--secondary-color);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.2s;
        }
        
        .sidebar .nav-link:hover {
            background: #e2e8f0;
            color: var(--primary-color);
        }
        
        .sidebar .nav-link.active {
            background: var(--primary-color);
            color: white;
        }
        
        .content-area {
            padding: 2rem;
        }
        
        .endpoint-card {
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            margin-bottom: 2rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .endpoint-header {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .method-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            text-transform: uppercase;
        }
        
        .method-get { background: #dcfce7; color: #166534; }
        .method-post { background: #dbeafe; color: #1e40af; }
        .method-put { background: #fef3c7; color: #92400e; }
        .method-delete { background: #fee2e2; color: #991b1b; }
        
        .endpoint-body {
            padding: 1.5rem;
        }
        
        .auth-required {
            background: #fef3c7;
            color: #92400e;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .code-block {
            background: #1e293b;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .table-responsive {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .table th {
            background: #f1f5f9;
            border: none;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .table td {
            border-color: #e2e8f0;
        }
        
        .required {
            color: var(--danger-color);
            font-weight: 600;
        }
        
        .optional {
            color: var(--secondary-color);
            font-style: italic;
        }
        
        .section-title {
            color: var(--dark-color);
            font-weight: 700;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
        }
        
        .alert-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .copy-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .response-example {
            position: relative;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-wallet me-2"></i>
                Arefan Wallet API
            </a>
            <span class="navbar-text text-white-50">
                Customer Mobile App Documentation
            </span>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column py-3">
                    <a class="nav-link active" href="#overview">
                        <i class="fas fa-home me-2"></i>Overview
                    </a>
                    <a class="nav-link" href="#authentication">
                        <i class="fas fa-key me-2"></i>Authentication
                    </a>
                    <a class="nav-link" href="#config">
                        <i class="fas fa-cog me-2"></i>Configuration
                    </a>
                    <a class="nav-link" href="#auth-endpoints">
                        <i class="fas fa-sign-in-alt me-2"></i>Auth Endpoints
                    </a>
                    <a class="nav-link" href="#profile">
                        <i class="fas fa-user me-2"></i>Profile Management
                    </a>
                    <a class="nav-link" href="#transactions">
                        <i class="fas fa-exchange-alt me-2"></i>Transactions
                    </a>
                    <a class="nav-link" href="#notifications">
                        <i class="fas fa-bell me-2"></i>Notifications
                    </a>
                    <a class="nav-link" href="#banners">
                        <i class="fas fa-image me-2"></i>Banners
                    </a>
                    <a class="nav-link" href="#errors">
                        <i class="fas fa-exclamation-triangle me-2"></i>Error Codes
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content-area">
                <!-- Overview Section -->
                <section id="overview">
                    <h1 class="section-title">
                        <i class="fas fa-home me-2"></i>API Overview
                    </h1>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Base URL:</strong> <code>http://127.0.0.1:8000/api/v1</code>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-mobile-alt me-2"></i>Mobile App API
                                    </h5>
                                    <p class="card-text">
                                        Complete REST API for Arefan Wallet mobile application with customer authentication, 
                                        transactions, and account management features.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <i class="fas fa-shield-alt me-2"></i>Security
                                    </h5>
                                    <p class="card-text">
                                        JWT-based authentication with device verification, PIN protection, 
                                        and comprehensive validation for all transactions.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Authentication Section -->
                <section id="authentication" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-key me-2"></i>Authentication
                    </h2>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> All API requests must include specific headers for device verification and authentication.
                    </div>

                    <h4>Required Headers</h4>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Header</th>
                                    <th>Value</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>Content-Type</code></td>
                                    <td><code>application/json</code></td>
                                    <td><span class="required">Yes</span></td>
                                    <td>Content type for JSON requests</td>
                                </tr>
                                <tr>
                                    <td><code>User-Agent</code></td>
                                    <td><code>Dart/2.17 (dart:io)</code></td>
                                    <td><span class="required">Yes</span></td>
                                    <td>Mobile app identification</td>
                                </tr>
                                <tr>
                                    <td><code>device-id</code></td>
                                    <td><code>unique-device-id</code></td>
                                    <td><span class="required">Yes</span></td>
                                    <td>Unique device identifier</td>
                                </tr>
                                <tr>
                                    <td><code>os</code></td>
                                    <td><code>Android/iOS</code></td>
                                    <td><span class="required">Yes</span></td>
                                    <td>Operating system</td>
                                </tr>
                                <tr>
                                    <td><code>device-model</code></td>
                                    <td><code>Device Model</code></td>
                                    <td><span class="required">Yes</span></td>
                                    <td>Device model name</td>
                                </tr>
                                <tr>
                                    <td><code>browser</code></td>
                                    <td><code>Mobile App</code></td>
                                    <td><span class="optional">Optional</span></td>
                                    <td>Browser/App name</td>
                                </tr>
                                <tr>
                                    <td><code>Authorization</code></td>
                                    <td><code>Bearer {token}</code></td>
                                    <td><span class="required">Auth Required</span></td>
                                    <td>JWT token for authenticated endpoints</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h4>Example Headers</h4>
                    <div class="response-example">
                        <button class="copy-btn" onclick="copyToClipboard('headers-example')">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre class="code-block" id="headers-example"><code class="language-json">{
  "Content-Type": "application/json",
  "User-Agent": "Dart/2.17 (dart:io)",
  "device-id": "test-device-123",
  "os": "Android",
  "device-model": "Samsung Galaxy S21",
  "browser": "Mobile App",
  "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}</code></pre>
                    </div>
                </section>

                <!-- Configuration Section -->
                <section id="config" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-cog me-2"></i>Configuration
                    </h2>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-get">GET</span>
                                    <span class="ms-3 fw-bold">/config</span>
                                </div>
                                <small class="text-muted">Public Endpoint</small>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Get application configuration including company details, currency settings, payment methods, and business settings.</p>

                            <h5>Response Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('config-response')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="config-response"><code class="language-json">{
  "company_name": "Arefan Wallet",
  "company_logo": "logo.png",
  "company_address": "123 Business Street",
  "company_phone": "+1234567890",
  "company_email": "<EMAIL>",
  "base_urls": {
    "customer_image_url": "http://127.0.0.1:8000/storage/app/public/customer",
    "agent_image_url": "http://127.0.0.1:8000/storage/app/public/agent",
    "notification_image_url": "http://127.0.0.1:8000/storage/app/public/notification"
  },
  "currency_decimal_point": 2,
  "currency_symbol": "$",
  "currency_symbol_position": "left",
  "terms_and_conditions": "Terms and conditions content...",
  "privacy_policy": "Privacy policy content...",
  "about_us": "About us content...",
  "phone_verification": true,
  "email_verification": false,
  "user_app_theme": "light",
  "software_version": "1.0.0",
  "language_code": "en",
  "active_payment_method_list": [
    {
      "gateway": "stripe",
      "gateway_title": "Stripe",
      "gateway_image": "stripe.png",
      "mode": "test"
    }
  ],
  "otp_resend_time": 60,
  "agent_self_registration": 1
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Auth Endpoints Section -->
                <section id="auth-endpoints" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-sign-in-alt me-2"></i>Authentication Endpoints
                    </h2>

                    <!-- Customer Registration -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/auth/register</span>
                                </div>
                                <small class="text-muted">Public Endpoint</small>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Register a new customer account. Requires phone verification via OTP.</p>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> You must call <code>/customer/auth/check-phone</code> first to send OTP before registration.
                            </div>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>f_name</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>First name</td>
                                        </tr>
                                        <tr>
                                            <td><code>l_name</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Last name</td>
                                        </tr>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Phone number (5-20 digits)</td>
                                        </tr>
                                        <tr>
                                            <td><code>dial_country_code</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Country code (e.g., +880)</td>
                                        </tr>
                                        <tr>
                                            <td><code>email</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>password</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Password (exactly 4 characters)</td>
                                        </tr>
                                        <tr>
                                            <td><code>gender</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Gender (male/female)</td>
                                        </tr>
                                        <tr>
                                            <td><code>occupation</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Occupation</td>
                                        </tr>
                                        <tr>
                                            <td><code>otp</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>OTP received from check-phone</td>
                                        </tr>
                                        <tr>
                                            <td><code>referral_code</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Referral code</td>
                                        </tr>
                                        <tr>
                                            <td><code>image</code></td>
                                            <td>file</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Profile image (jpeg,jpg,png,gif, max 10MB)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('register-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="register-request"><code class="language-json">{
  "f_name": "John",
  "l_name": "Doe",
  "phone": "1234567890",
  "dial_country_code": "+880",
  "email": "<EMAIL>",
  "password": "1234",
  "gender": "male",
  "occupation": "Engineer",
  "otp": "1234",
  "referral_code": ""
}</code></pre>
                            </div>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('register-success')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="register-success"><code class="language-json">{
  "message": "Registration Successful"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Check Phone -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/auth/check-phone</span>
                                </div>
                                <small class="text-muted">Public Endpoint</small>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Check phone number availability and send OTP for registration.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Full phone number with country code (e.g., +8801234567890)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('check-phone-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="check-phone-request"><code class="language-json">{
  "phone": "+8801234567890"
}</code></pre>
                            </div>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('check-phone-success')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="check-phone-success"><code class="language-json">{
  "message": "Number is ready to register",
  "otp": "active"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Login -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/auth/login</span>
                                </div>
                                <small class="text-muted">Public Endpoint</small>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Login customer and get JWT authentication token.</p>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Important:</strong> Device headers (device-id, os, device-model, browser) are required for login to work properly.
                            </div>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Phone number (without country code)</td>
                                        </tr>
                                        <tr>
                                            <td><code>dial_country_code</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Country code (e.g., +880)</td>
                                        </tr>
                                        <tr>
                                            <td><code>password</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Password (4 characters)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('login-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="login-request"><code class="language-json">{
  "phone": "1234567890",
  "dial_country_code": "+880",
  "password": "1234"
}</code></pre>
                            </div>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('login-success')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="login-success"><code class="language-json">{
  "message": "successfully login",
  "content": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "is_phone_verified": 1,
  "phone_verification_status": 1
}</code></pre>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> The JWT token is in the <code>content</code> field, not <code>token</code>.
                            </div>
                        </div>
                    </div>

                    <!-- Forgot Password -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/auth/forgot-password</span>
                                </div>
                                <small class="text-muted">Public Endpoint</small>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Send OTP for password reset.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Full phone number with country code</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('forgot-password-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="forgot-password-request"><code class="language-json">{
  "phone": "+8801234567890"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Reset Password -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/auth/reset-password</span>
                                </div>
                                <small class="text-muted">Public Endpoint</small>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Reset password using OTP verification.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Full phone number with country code</td>
                                        </tr>
                                        <tr>
                                            <td><code>otp</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>OTP received from forgot-password</td>
                                        </tr>
                                        <tr>
                                            <td><code>password</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>New password (4 characters)</td>
                                        </tr>
                                        <tr>
                                            <td><code>confirm_password</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Confirm new password</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('reset-password-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="reset-password-request"><code class="language-json">{
  "phone": "+8801234567890",
  "otp": "1234",
  "password": "5678",
  "confirm_password": "5678"
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Profile Management Section -->
                <section id="profile" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-user me-2"></i>Profile Management
                    </h2>

                    <!-- Get Customer Profile -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-get">GET</span>
                                    <span class="ms-3 fw-bold">/customer/get-customer</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Get authenticated customer profile information including balance and KYC status.</p>

                            <h5>Response Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('get-customer-response')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="get-customer-response"><code class="language-json">{
  "id": 1,
  "f_name": "John",
  "l_name": "Doe",
  "phone": "1234567890",
  "email": "<EMAIL>",
  "image": "customer-image.jpg",
  "type": 2,
  "is_kyc_verified": 1,
  "is_phone_verified": 1,
  "is_email_verified": 0,
  "gender": "male",
  "occupation": "Engineer",
  "balance": "1000.00",
  "pending_balance": "0.00",
  "created_at": "2023-01-01T00:00:00.000000Z"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Update Profile -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/update-profile</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Update customer profile information.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>f_name</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>First name</td>
                                        </tr>
                                        <tr>
                                            <td><code>l_name</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Last name</td>
                                        </tr>
                                        <tr>
                                            <td><code>email</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>gender</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Gender (male/female)</td>
                                        </tr>
                                        <tr>
                                            <td><code>occupation</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Occupation</td>
                                        </tr>
                                        <tr>
                                            <td><code>image</code></td>
                                            <td>file</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Profile image</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Verify PIN -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/verify-pin</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Verify customer PIN for secure operations.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>4-digit PIN</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('verify-pin-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="verify-pin-request"><code class="language-json">{
  "pin": "1234"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Change PIN -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/change-pin</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Change customer PIN.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>old_pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Current PIN (4 digits)</td>
                                        </tr>
                                        <tr>
                                            <td><code>new_pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>New PIN (4 digits)</td>
                                        </tr>
                                        <tr>
                                            <td><code>confirm_pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Confirm new PIN</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Logout -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/logout</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Logout customer and invalidate JWT token.</p>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('logout-response')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="logout-response"><code class="language-json">{
  "message": "Successfully logged out"
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Transactions Section -->
                <section id="transactions" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-exchange-alt me-2"></i>Transaction Endpoints
                    </h2>

                    <!-- Send Money -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/send-money</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Send money to another customer. Requires PIN verification and sufficient balance.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>4-digit PIN for verification</td>
                                        </tr>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Receiver's phone number</td>
                                        </tr>
                                        <tr>
                                            <td><code>amount</code></td>
                                            <td>numeric</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Amount to send (must be greater than 0)</td>
                                        </tr>
                                        <tr>
                                            <td><code>purpose</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Purpose of transaction</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('send-money-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="send-money-request"><code class="language-json">{
  "pin": "1234",
  "phone": "+8801987654321",
  "amount": 100.50,
  "purpose": "Payment for services"
}</code></pre>
                            </div>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('send-money-success')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="send-money-success"><code class="language-json">{
  "message": "success",
  "transaction_id": "TXN123456789"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Out -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/cash-out</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Cash out money through an agent. Requires PIN verification.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>4-digit PIN for verification</td>
                                        </tr>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Agent's phone number</td>
                                        </tr>
                                        <tr>
                                            <td><code>amount</code></td>
                                            <td>numeric</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Amount to cash out (must be greater than 0)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('cash-out-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="cash-out-request"><code class="language-json">{
  "pin": "1234",
  "phone": "+8801555666777",
  "amount": 500.00
}</code></pre>
                            </div>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('cash-out-success')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="cash-out-success"><code class="language-json">{
  "message": "success",
  "transaction_id": "TXN987654321"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Request Money -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/request-money</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Request money from another customer.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>phone</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Phone number to request money from</td>
                                        </tr>
                                        <tr>
                                            <td><code>amount</code></td>
                                            <td>numeric</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Amount to request (must be greater than 0)</td>
                                        </tr>
                                        <tr>
                                            <td><code>note</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Note for the request</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Request Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('request-money-request')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="request-money-request"><code class="language-json">{
  "phone": "+8801987654321",
  "amount": 250.00,
  "note": "Payment for lunch"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Add Money -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/add-money</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Add money to wallet using payment gateway. Requires KYC verification.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>amount</code></td>
                                            <td>numeric</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Amount to add</td>
                                        </tr>
                                        <tr>
                                            <td><code>payment_method</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Payment method (stripe, paypal, etc.)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Success Response</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('add-money-success')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="add-money-success"><code class="language-json">{
  "link": "http://127.0.0.1:8000/payment-mobile?user_id=1&amount=100&payment_method=stripe"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Withdraw -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-post">POST</span>
                                    <span class="ms-3 fw-bold">/customer/withdraw</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Withdraw money from wallet. Requires PIN verification and sufficient balance.</p>

                            <h5>Request Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>pin</code></td>
                                            <td>string</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>4-digit PIN for verification</td>
                                        </tr>
                                        <tr>
                                            <td><code>amount</code></td>
                                            <td>numeric</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Amount to withdraw (must be greater than 0)</td>
                                        </tr>
                                        <tr>
                                            <td><code>note</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Withdrawal note (max 255 characters)</td>
                                        </tr>
                                        <tr>
                                            <td><code>withdrawal_method_id</code></td>
                                            <td>integer</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Withdrawal method ID</td>
                                        </tr>
                                        <tr>
                                            <td><code>withdrawal_method_fields</code></td>
                                            <td>object</td>
                                            <td><span class="required">Yes</span></td>
                                            <td>Method-specific fields (bank account, etc.)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction History -->
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-get">GET</span>
                                    <span class="ms-3 fw-bold">/customer/transaction-history</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Get customer transaction history with pagination and filtering.</p>

                            <h5>Query Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>limit</code></td>
                                            <td>integer</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Number of records per page (default: 10)</td>
                                        </tr>
                                        <tr>
                                            <td><code>offset</code></td>
                                            <td>integer</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Page number (default: 1)</td>
                                        </tr>
                                        <tr>
                                            <td><code>transaction_type</code></td>
                                            <td>string</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Filter by type (send_money, received_money, add_money, etc.)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Response Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('transaction-history-response')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="transaction-history-response"><code class="language-json">{
  "total_size": 25,
  "limit": 10,
  "offset": 1,
  "transactions": [
    {
      "id": 1,
      "transaction_type": "send_money",
      "amount": "100.00",
      "charge": "2.00",
      "balance": "898.00",
      "transaction_id": "TXN123456789",
      "to_user": {
        "f_name": "Jane",
        "l_name": "Smith",
        "phone": "+8801987654321"
      },
      "created_at": "2023-01-01T12:00:00.000000Z"
    }
  ]
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Notifications Section -->
                <section id="notifications" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-bell me-2"></i>Notifications
                    </h2>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-get">GET</span>
                                    <span class="ms-3 fw-bold">/customer/get-notification</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Get customer notifications with pagination.</p>

                            <h5>Query Parameters</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>limit</code></td>
                                            <td>integer</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Number of notifications per page (default: 10)</td>
                                        </tr>
                                        <tr>
                                            <td><code>offset</code></td>
                                            <td>integer</td>
                                            <td><span class="optional">Optional</span></td>
                                            <td>Page number (default: 1)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Response Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('notifications-response')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="notifications-response"><code class="language-json">{
  "total_size": 15,
  "limit": 10,
  "offset": 1,
  "notifications": [
    {
      "id": 1,
      "title": "Welcome to Arefan Wallet",
      "description": "Thank you for joining our platform!",
      "image": "notification1.png",
      "created_at": "2023-01-01T10:00:00.000000Z"
    }
  ]
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Banners Section -->
                <section id="banners" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-image me-2"></i>Banners
                    </h2>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="method-badge method-get">GET</span>
                                    <span class="ms-3 fw-bold">/customer/get-banner</span>
                                </div>
                                <div class="auth-required">
                                    <i class="fas fa-lock me-1"></i>Auth Required
                                </div>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <p>Get active banners for customer app.</p>

                            <h5>Response Example</h5>
                            <div class="response-example">
                                <button class="copy-btn" onclick="copyToClipboard('banners-response')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <pre class="code-block" id="banners-response"><code class="language-json">[
  {
    "title": "Special Offer",
    "image": "banner1.jpg",
    "url": "https://example.com/offer",
    "receiver": "customers"
  },
  {
    "title": "New Feature",
    "image": "banner2.jpg",
    "url": "https://example.com/feature",
    "receiver": "all"
  }
]</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Error Codes Section -->
                <section id="errors" class="mt-5">
                    <h2 class="section-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>Error Codes & Troubleshooting
                    </h2>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Common Issues Solved:</strong> Based on API testing, here are the most common issues and their solutions.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-times-circle me-2"></i>Authentication Errors
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-danger">AUTH_LOGIN_400</h6>
                                    <p><strong>Cause:</strong> Missing device headers during login</p>
                                    <p><strong>Solution:</strong> Include all required headers: <code>device-id</code>, <code>os</code>, <code>device-model</code>, <code>browser</code></p>

                                    <h6 class="text-danger mt-3">OTP is not found!</h6>
                                    <p><strong>Cause:</strong> Trying to register without calling check-phone first</p>
                                    <p><strong>Solution:</strong> Call <code>/customer/auth/check-phone</code> before registration</p>

                                    <h6 class="text-danger mt-3">PIN is incorrect</h6>
                                    <p><strong>Cause:</strong> Wrong PIN provided for transactions</p>
                                    <p><strong>Solution:</strong> Verify PIN is exactly 4 digits and correct</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Validation Errors
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-warning">Password validation failed</h6>
                                    <p><strong>Cause:</strong> Password not exactly 4 characters</p>
                                    <p><strong>Solution:</strong> Use exactly 4-character password</p>

                                    <h6 class="text-warning mt-3">Missing required fields</h6>
                                    <p><strong>Cause:</strong> Gender or occupation missing in registration</p>
                                    <p><strong>Solution:</strong> Include all required fields in registration</p>

                                    <h6 class="text-warning mt-3">Verify your account information</h6>
                                    <p><strong>Cause:</strong> KYC not verified for add-money</p>
                                    <p><strong>Solution:</strong> Complete KYC verification first</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h4>HTTP Status Codes</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Status Code</th>
                                        <th>Meaning</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge bg-success">200</span></td>
                                        <td>OK</td>
                                        <td>Request successful</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-warning">400</span></td>
                                        <td>Bad Request</td>
                                        <td>Invalid request parameters</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-danger">401</span></td>
                                        <td>Unauthorized</td>
                                        <td>Authentication required or failed</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-danger">403</span></td>
                                        <td>Forbidden</td>
                                        <td>Access denied or validation failed</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-secondary">404</span></td>
                                        <td>Not Found</td>
                                        <td>Endpoint or resource not found</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-danger">500</span></td>
                                        <td>Internal Server Error</td>
                                        <td>Server-side error</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // Copy to clipboard function
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                // Show success feedback
                const btn = element.parentElement.querySelector('.copy-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                }, 2000);
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                    
                    // Update active nav link
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Update active nav link on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
