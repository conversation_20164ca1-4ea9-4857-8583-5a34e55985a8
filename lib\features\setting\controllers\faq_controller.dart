import 'package:six_cash/features/setting/domain/models/faq_model.dart';
import 'package:six_cash/features/setting/domain/reposotories/faq_repo.dart';
import 'package:get/get.dart';
import 'package:six_cash/util/app_constants.dart';

class FaqController extends GetxController implements GetxService {
  final FaqRepo faqrepo;
  FaqController({required this.faqrepo});

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<HelpTopic>? _helpTopics;
  List<HelpTopic>? get helpTopics => _helpTopics;


  Future getFaqList() async{
    _isLoading = true;
    _helpTopics = [];

    // Demo mode handling
    if (AppConstants.demo) {
      List<Map<String, dynamic>> demoFaqs = [
        {
          'id': 1,
          'question': 'How do I send money?',
          'answer': 'To send money, go to the home screen, tap "Send Money", enter the recipient\'s phone number, amount, and confirm with your PIN.',
          'ranking': 1,
          'status': 1,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z'
        },
        {
          'id': 2,
          'question': 'How do I add money to my wallet?',
          'answer': 'You can add money by tapping "Add Money" on the home screen, selecting your preferred payment method, and following the instructions.',
          'ranking': 2,
          'status': 1,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z'
        },
        {
          'id': 3,
          'question': 'Is my money safe?',
          'answer': 'Yes, your money is completely safe. We use bank-level security and encryption to protect all transactions and personal information.',
          'ranking': 3,
          'status': 1,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z'
        },
        {
          'id': 4,
          'question': 'What are the transaction limits?',
          'answer': 'Daily transaction limits vary based on your verification level. Basic users can send up to ₦50,000 per day, while verified users can send up to ₦500,000 per day.',
          'ranking': 4,
          'status': 1,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z'
        },
        {
          'id': 5,
          'question': 'How do I withdraw money?',
          'answer': 'To withdraw money, go to "Cash Out" from the home screen, select your withdrawal method (bank transfer or mobile money), enter the amount and details.',
          'ranking': 5,
          'status': 1,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z'
        },
        {
          'id': 6,
          'question': 'How long do transactions take?',
          'answer': 'Most transactions are instant. Bank transfers may take 1-3 business days depending on your bank.',
          'ranking': 6,
          'status': 1,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z'
        }
      ];

      _helpTopics = FaqModel.fromJson({
        'help_topics': demoFaqs
      }).helpTopics;

      _isLoading = false;
      update();
      return;
    }

    Response response = await faqrepo.getFaqList();
    if(response.body != null && response.body != {} && response.statusCode == 200){
      _helpTopics =   FaqModel.fromJson(response.body).helpTopics;
      _isLoading = false;
      update();
    }
  }
}