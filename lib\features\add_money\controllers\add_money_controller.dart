import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/features/add_money/domain/reposotories/add_money_repo.dart';
import 'package:get/get.dart';
import 'package:six_cash/features/add_money/screens/web_screen.dart';
import 'package:six_cash/util/app_constants.dart';
import 'package:six_cash/helper/custom_snackbar_helper.dart';

class AddMoneyController extends GetxController implements GetxService {
  final AddMoneyRepo addMoneyRepo;
  AddMoneyController({required this.addMoneyRepo});

 bool  _isLoading = false;
  String? _addMoneyWebLink;
  bool get isLoading => _isLoading;
  String? get addMoneyWebLink => _addMoneyWebLink;

  String? _paymentMethod;
  String? get paymentMethod => _paymentMethod;




  Future<void> addMoney(double amount) async{
    _isLoading = true;
    update();

    // Demo mode handling
    if (AppConstants.demo) {
      await Future.delayed(const Duration(seconds: 2));
      _isLoading = false;
      update();

      // Simulate successful add money operation
      showCustomSnackBarHelper(
        'Demo Mode: ₦${amount.toStringAsFixed(2)} added successfully to your wallet!',
        isError: false
      );
      Get.back(); // Go back to previous screen
      return;
    }

    Response response = await addMoneyRepo.addMoneyApi(amount : amount, paymentMethod: _paymentMethod!);
    if(response.statusCode == 200){
     _addMoneyWebLink =  response.body['link'];
     if(_addMoneyWebLink != null){
       Get.offAll(()=> WebScreen(selectedUrl: _addMoneyWebLink!, isPaymentUrl: true));
     }
    }else{
      ApiChecker.checkApi(response);
    }

    _isLoading = false;
    update();


  }


  void setPaymentMethod(String? method, {isUpdate = true}) {
    _paymentMethod = method;
   if(isUpdate){
     update();
   }
  }

}