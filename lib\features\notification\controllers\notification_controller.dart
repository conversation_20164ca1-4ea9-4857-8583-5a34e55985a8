import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/features/notification/domain/models/notification_model.dart';
import 'package:get/get.dart';
import 'package:six_cash/features/notification/domain/reposotories/notification_repo.dart';
import 'package:six_cash/util/app_constants.dart';

class NotificationController extends GetxController implements GetxService {
  final NotificationRepo notificationRepo;
  NotificationController({required this.notificationRepo});

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<Notifications> _notificationList = [];
  List<Notifications> get notificationList => _notificationList;

  NotificationModel? _notificationModel;
  NotificationModel? get notificationModel => _notificationModel;

  Future getNotificationList(bool reload, {int offset = 1}) async{

    if(reload){
      _notificationModel = null;
      _notificationList = [];
    }

    if(_notificationModel == null || offset != 1){
      // Demo mode handling
      if (AppConstants.demo) {
        List<Map<String, dynamic>> demoNotifications = [
          {
            'id': 1,
            'title': 'Welcome to 6Cash!',
            'description': 'Your account has been successfully created. Start sending and receiving money instantly.',
            'image': 'welcome_notification.png',
            'created_at': '2024-01-15T10:00:00Z',
            'updated_at': '2024-01-15T10:00:00Z'
          },
          {
            'id': 2,
            'title': 'Money Received',
            'description': 'You have received ₦2,000.00 from Alice Johnson (+*************)',
            'image': 'money_received.png',
            'created_at': '2024-01-14T16:45:00Z',
            'updated_at': '2024-01-14T16:45:00Z'
          },
          {
            'id': 3,
            'title': 'Transaction Successful',
            'description': 'Your money transfer of ₦1,500.00 to John Doe was successful.',
            'image': 'transaction_success.png',
            'created_at': '2024-01-13T14:20:00Z',
            'updated_at': '2024-01-13T14:20:00Z'
          },
          {
            'id': 4,
            'title': 'Security Alert',
            'description': 'Your account was accessed from a new device. If this wasn\'t you, please contact support.',
            'image': 'security_alert.png',
            'created_at': '2024-01-12T09:30:00Z',
            'updated_at': '2024-01-12T09:30:00Z'
          },
          {
            'id': 5,
            'title': 'Wallet Top-up Successful',
            'description': 'Your wallet has been topped up with ₦5,000.00 successfully.',
            'image': 'wallet_topup.png',
            'created_at': '2024-01-11T11:15:00Z',
            'updated_at': '2024-01-11T11:15:00Z'
          }
        ];

        _notificationModel = NotificationModel.fromJson({
          'total_size': demoNotifications.length,
          'limit': 20,
          'offset': offset,
          'notifications': demoNotifications
        });

        if(offset != 1){
          _notificationList.addAll( _notificationModel?.notifications ?? [] );
        }else{
          _notificationList = [];
          _notificationList.addAll( _notificationModel?.notifications ?? [] );
        }

        _isLoading = false;
        update();
        return;
      }

      Response response = await notificationRepo.getNotificationList(offset);
      if(response.body != null && response.body != {} && response.statusCode == 200){
        _notificationModel = NotificationModel.fromJson(response.body);

        if(offset != 1){
          _notificationList.addAll( _notificationModel?.notifications ?? [] );
        }else{
          _notificationList = [];
          _notificationList.addAll( _notificationModel?.notifications ?? [] );
        }
      }else {
        ApiChecker.checkApi(response);
      }
      _isLoading = false;
      update();
    }

  }
}