<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>410522356318-lqa45bo7d289g8k4ns816b6g76qqmdhb.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.410522356318-lqa45bo7d289g8k4ns816b6g76qqmdhb</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>410522356318-a06iuh8uimk0bpbq0jj96n1kbigrebre.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyA8lpofCi358diupbxu_E8l29g349P1INc</string>
	<key>GCM_SENDER_ID</key>
	<string>410522356318</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.u6amtech.flutterRestaurant</string>
	<key>PROJECT_ID</key>
	<string>e-food-9e6e3</string>
	<key>STORAGE_BUCKET</key>
	<string>e-food-9e6e3.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:410522356318:ios:6782d2452ecd04473dc2cf</string>
</dict>
</plist>