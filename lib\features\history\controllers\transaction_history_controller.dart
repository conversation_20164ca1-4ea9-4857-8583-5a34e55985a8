import 'package:six_cash/data/api/api_checker.dart';
import 'package:six_cash/features/history/domain/models/transaction_model.dart';
import 'package:six_cash/features/history/domain/reposotories/transaction_history_repo.dart';
import 'package:get/get.dart';
import 'package:six_cash/util/app_constants.dart';

class TransactionHistoryController extends GetxController implements GetxService{
  final TransactionHistoryRepo transactionHistoryRepo;
  TransactionHistoryController({required this.transactionHistoryRepo});

  int? _pageSize;
  bool _isLoading = false;
  bool _firstLoading = true;
  bool get firstLoading => _firstLoading;
  int _offset = 1;
  int get offset =>_offset;
  int _transactionTypeIndex = 0;


  List<int> _offsetList = [];
  List<int> get offsetList => _offsetList;

  List<Transactions> _transactionList  = [];
  List<Transactions> get transactionList => _transactionList;

  List<Transactions> _sendMoneyList = [];
  List<Transactions> get sendMoneyList => _sendMoneyList;

  List<Transactions> _cashInMoneyList = [];
  List<Transactions> get cashInMoneyList => _cashInMoneyList;

  List<Transactions> _addMoneyList = [];
  List<Transactions> get addMoneyList => _addMoneyList;

  List<Transactions> _receivedMoneyList = [];
  List<Transactions> get receivedMoneyList => _receivedMoneyList;

  List<Transactions> _cashOutList = [];
  List<Transactions> get cashOutList => _cashOutList;

  List<Transactions> _withdrawList = [];
  List<Transactions> get withdrawList => _withdrawList;

  List<Transactions> _paymentList = [];
  List<Transactions> get paymentList => _paymentList;



  int? get pageSize => _pageSize;
  bool get isLoading => _isLoading;
  int get transactionTypeIndex => _transactionTypeIndex;

  void showBottomLoader() {
    _isLoading = true;
    update();
  }


  Future getTransactionData(int offset, {bool reload = false}) async{
    if(reload) {
      _offsetList = [];
      _transactionList = [];
      _sendMoneyList = [];
      _cashInMoneyList = [];
      _addMoneyList = [];
      _receivedMoneyList =[];
      _cashOutList = [];
      _withdrawList = [];
      _paymentList = [];
    }
    _offset = offset;
    if(!_offsetList.contains(offset)) {
      _offsetList.add(offset);

      // Demo mode handling
      if (AppConstants.demo) {
        // Create demo transaction data
        List<Map<String, dynamic>> demoTransactions = [
          {
            'id': 1,
            'transaction_id': 'TXN001',
            'transaction_type': AppConstants.sendMoney,
            'amount': 1500.0,
            'charge': 15.0,
            'balance': 6000.0,
            'receiver': {'name': 'John Doe', 'phone': '+2348123456789'},
            'sender': {'name': 'Demo User', 'phone': '+2347026591356'},
            'created_at': '2024-01-15T10:30:00Z',
            'status': 'success'
          },
          {
            'id': 2,
            'transaction_id': 'TXN002',
            'transaction_type': AppConstants.receivedMoney,
            'amount': 2000.0,
            'charge': 0.0,
            'balance': 7500.0,
            'receiver': {'name': 'Demo User', 'phone': '+2347026591356'},
            'sender': {'name': 'Alice Johnson', 'phone': '+2349087654321'},
            'created_at': '2024-01-14T16:45:00Z',
            'status': 'success'
          },
          {
            'id': 3,
            'transaction_id': 'TXN003',
            'transaction_type': AppConstants.addMoney,
            'amount': 5000.0,
            'charge': 50.0,
            'balance': 5500.0,
            'created_at': '2024-01-13T09:15:00Z',
            'status': 'success'
          },
          {
            'id': 4,
            'transaction_id': 'TXN004',
            'transaction_type': AppConstants.cashOut,
            'amount': 1000.0,
            'charge': 25.0,
            'balance': 4475.0,
            'receiver': {'name': 'Agent Store', 'phone': '+2348765432109'},
            'created_at': '2024-01-12T14:20:00Z',
            'status': 'success'
          },
          {
            'id': 5,
            'transaction_id': 'TXN005',
            'transaction_type': AppConstants.withdraw,
            'amount': 3000.0,
            'charge': 30.0,
            'balance': 1445.0,
            'created_at': '2024-01-11T11:00:00Z',
            'status': 'pending'
          }
        ];

        _transactionList = [];
        _sendMoneyList = [];
        _cashInMoneyList = [];
        _addMoneyList = [];
        _receivedMoneyList =[];
        _cashOutList = [];
        _withdrawList = [];
        _paymentList = [];

        for (var transactionData in demoTransactions) {
          Transactions history = Transactions.fromJson(transactionData);
          if(history.transactionType == AppConstants.sendMoney){
            _sendMoneyList.add(history);
          }else if(history.transactionType == AppConstants.cashIn){
            _cashInMoneyList.add(history);
          }else if(history.transactionType == AppConstants.addMoney){
            _addMoneyList.add(history);
          }else if(history.transactionType == AppConstants.receivedMoney){
            _receivedMoneyList.add(history);
          }else if(history.transactionType == AppConstants.withdraw){
            _withdrawList.add(history);
          }else if(history.transactionType == AppConstants.payment){
            _paymentList.add(history);
          }else if(history.transactionType == AppConstants.cashOut){
            _cashOutList.add(history);
          }
          _transactionList.add(history);
        }
        _pageSize = demoTransactions.length;

        _isLoading = false;
        _firstLoading = false;
        update();
        return;
      }

      Response response = await transactionHistoryRepo.getTransactionHistory(offset);
      if(response.body['transactions'] != null && response.body['transactions'] != {} && response.statusCode==200){
        _transactionList = [];
        _sendMoneyList = [];
        _cashInMoneyList = [];
        _addMoneyList = [];
        _receivedMoneyList =[];
        _cashOutList = [];
        _withdrawList = [];
        _paymentList = [];
        response.body['transactions'].forEach((transactionHistory) {
          Transactions history = Transactions.fromJson(transactionHistory);
          if(history.transactionType == AppConstants.sendMoney){
            _sendMoneyList.add(history);
          }else if(history.transactionType == AppConstants.cashIn){
            _cashInMoneyList.add(history);
          }else if(history.transactionType == AppConstants.addMoney){
            _addMoneyList.add(history);
          }else if(history.transactionType == AppConstants.receivedMoney){
            _receivedMoneyList.add(history);
          }else if(history.transactionType == AppConstants.withdraw){
            _withdrawList.add(history);
          }else if(history.transactionType == AppConstants.payment){
            _paymentList.add(history);
          }else if(history.transactionType == AppConstants.cashOut){
            _cashOutList.add(history);
          }_transactionList.add(history);
        });
        _pageSize = TransactionModel.fromJson(response.body).totalSize;
      }else{
        ApiChecker.checkApi(response);
      }

      _isLoading = false;
      _firstLoading = false;
      update();
    }

  }

  void setIndex(int index) {
    _transactionTypeIndex = index;
    update();
  }

}